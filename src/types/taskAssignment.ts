export interface TaskAssignment {
  id: number;
  taskId: number;
  userId: number;
  assignedAt: Date;
  assignedBy?: number;
  isLeader: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface TaskAssignmentWithUser {
  id: number;
  taskId: number;
  userId: number;
  assignedAt: Date;
  assignedBy?: number;
  isLeader: boolean;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
  // User details
  userEmail?: string;
  userFirstName?: string;
  userLastName?: string;
  userImageUrl?: string;
  // Assigned by user details
  assignedByEmail?: string;
  assignedByFirstName?: string;
  assignedByLastName?: string;
}

export interface TaskAssignmentFilters {
  taskId?: number;
  userId?: number;
  assignedBy?: number;
  isLeader?: boolean;
  isActive?: boolean;
  limit?: number;
  offset?: number;
}

export interface CreateTaskAssignmentData {
  taskId: number;
  userId: number;
  assignedBy?: number;
  isLeader?: boolean;
  isActive?: boolean;
}

export interface UpdateTaskAssignmentData {
  assignedBy?: number;
  isLeader?: boolean;
  isActive?: boolean;
}
