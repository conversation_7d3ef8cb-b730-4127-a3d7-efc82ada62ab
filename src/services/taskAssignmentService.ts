import { QueryResult } from 'pg';
import { db } from '../database';
import { 
  TaskAssignment, 
  TaskAssignmentWithUser,
  TaskAssignmentFilters,
  CreateTaskAssignmentData,
  UpdateTaskAssignmentData
} from '../types/taskAssignment';

/**
 * Get user IDs and assignedBy information by task ID
 */
export async function getUsersByTaskId(taskId: number): Promise<TaskAssignmentWithUser[]> {
  const query = `
    SELECT 
      ta.id,
      ta.task_id as "taskId",
      ta.user_id as "userId",
      ta.assigned_at as "assignedAt",
      ta.assigned_by as "assignedBy",
      ta.is_leader as "isLeader",
      ta.is_active as "isActive",
      ta.created_at as "createdAt",
      ta.updated_at as "updatedAt",
      u.email as "userEmail",
      u.first_name as "userFirstName",
      u.last_name as "userLastName",
      u.image_url as "userImageUrl",
      ab.email as "assignedByEmail",
      ab.first_name as "assignedByFirstName",
      ab.last_name as "assignedByLastName"
    FROM task_assignments ta
    INNER JOIN users u ON ta.user_id = u.id
    LEFT JOIN users ab ON ta.assigned_by = ab.id
    WHERE ta.task_id = $1 AND ta.is_active = true
    ORDER BY ta.assigned_at ASC
  `;

  try {
    const result: QueryResult = await db.query(query, [taskId]);
    return result.rows as TaskAssignmentWithUser[];
  } catch (error) {
    console.error('Error getting users by task ID:', error);
    throw new Error(`Failed to get users for task with ID: ${taskId}`);
  }
}

/**
 * Get a single task assignment by ID
 */
export async function getTaskAssignmentById(id: number): Promise<TaskAssignment | null> {
  const query = `
    SELECT 
      id,
      task_id as "taskId",
      user_id as "userId",
      assigned_at as "assignedAt",
      assigned_by as "assignedBy",
      is_leader as "isLeader",
      is_active as "isActive",
      created_at as "createdAt",
      updated_at as "updatedAt"
    FROM task_assignments
    WHERE id = $1
  `;

  try {
    const result: QueryResult = await db.query(query, [id]);

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0] as TaskAssignment;
  } catch (error) {
    console.error('Error getting task assignment by ID:', error);
    throw new Error(`Failed to get task assignment with ID: ${id}`);
  }
}

/**
 * Get task assignments with filters
 */
export async function getTaskAssignments(filters: TaskAssignmentFilters = {}): Promise<TaskAssignment[]> {
  const {
    taskId,
    userId,
    assignedBy,
    isLeader,
    isActive,
    limit = 50,
    offset = 0
  } = filters;

  let query = `
    SELECT 
      id,
      task_id as "taskId",
      user_id as "userId",
      assigned_at as "assignedAt",
      assigned_by as "assignedBy",
      is_leader as "isLeader",
      is_active as "isActive",
      created_at as "createdAt",
      updated_at as "updatedAt"
    FROM task_assignments
  `;

  const conditions: string[] = [];
  const params: any[] = [];
  let paramIndex = 1;

  if (taskId !== undefined) {
    conditions.push(`task_id = $${paramIndex++}`);
    params.push(taskId);
  }

  if (userId !== undefined) {
    conditions.push(`user_id = $${paramIndex++}`);
    params.push(userId);
  }

  if (assignedBy !== undefined) {
    conditions.push(`assigned_by = $${paramIndex++}`);
    params.push(assignedBy);
  }

  if (isLeader !== undefined) {
    conditions.push(`is_leader = $${paramIndex++}`);
    params.push(isLeader);
  }

  if (isActive !== undefined) {
    conditions.push(`is_active = $${paramIndex++}`);
    params.push(isActive);
  }

  if (conditions.length > 0) {
    query += ' WHERE ' + conditions.join(' AND ');
  }

  query += ` ORDER BY assigned_at DESC LIMIT $${paramIndex++} OFFSET $${paramIndex++}`;
  params.push(limit, offset);

  try {
    const result: QueryResult = await db.query(query, params);
    return result.rows as TaskAssignment[];
  } catch (error) {
    console.error('Error getting task assignments:', error);
    throw new Error('Failed to get task assignments');
  }
}

/**
 * Create a new task assignment
 */
export async function createTaskAssignment(data: CreateTaskAssignmentData): Promise<TaskAssignment> {
  const {
    taskId,
    userId,
    assignedBy,
    isLeader = false,
    isActive = true
  } = data;

  const query = `
    INSERT INTO task_assignments (
      task_id, 
      user_id, 
      assigned_by, 
      is_leader, 
      is_active
    )
    VALUES ($1, $2, $3, $4, $5)
    RETURNING 
      id,
      task_id as "taskId",
      user_id as "userId",
      assigned_at as "assignedAt",
      assigned_by as "assignedBy",
      is_leader as "isLeader",
      is_active as "isActive",
      created_at as "createdAt",
      updated_at as "updatedAt"
  `;

  try {
    const result: QueryResult = await db.query(query, [
      taskId,
      userId,
      assignedBy,
      isLeader,
      isActive
    ]);

    return result.rows[0] as TaskAssignment;
  } catch (error) {
    console.error('Error creating task assignment:', error);
    throw new Error('Failed to create task assignment');
  }
}

/**
 * Update a task assignment
 */
export async function updateTaskAssignment(
  id: number, 
  data: UpdateTaskAssignmentData
): Promise<TaskAssignment | null> {
  const { assignedBy, isLeader, isActive } = data;

  const updates: string[] = [];
  const params: any[] = [];
  let paramIndex = 1;

  if (assignedBy !== undefined) {
    updates.push(`assigned_by = $${paramIndex++}`);
    params.push(assignedBy);
  }

  if (isLeader !== undefined) {
    updates.push(`is_leader = $${paramIndex++}`);
    params.push(isLeader);
  }

  if (isActive !== undefined) {
    updates.push(`is_active = $${paramIndex++}`);
    params.push(isActive);
  }

  if (updates.length === 0) {
    throw new Error('No fields to update');
  }

  updates.push(`updated_at = NOW()`);
  params.push(id);

  const query = `
    UPDATE task_assignments 
    SET ${updates.join(', ')}
    WHERE id = $${paramIndex}
    RETURNING 
      id,
      task_id as "taskId",
      user_id as "userId",
      assigned_at as "assignedAt",
      assigned_by as "assignedBy",
      is_leader as "isLeader",
      is_active as "isActive",
      created_at as "createdAt",
      updated_at as "updatedAt"
  `;

  try {
    const result: QueryResult = await db.query(query, params);

    if (result.rows.length === 0) {
      return null;
    }

    return result.rows[0] as TaskAssignment;
  } catch (error) {
    console.error('Error updating task assignment:', error);
    throw new Error(`Failed to update task assignment with ID: ${id}`);
  }
}

/**
 * Get task leaders by task ID
 */
export async function getTaskLeadersByTaskId(taskId: number): Promise<TaskAssignmentWithUser[]> {
  const query = `
    SELECT 
      ta.id,
      ta.task_id as "taskId",
      ta.user_id as "userId",
      ta.assigned_at as "assignedAt",
      ta.assigned_by as "assignedBy",
      ta.is_leader as "isLeader",
      ta.is_active as "isActive",
      ta.created_at as "createdAt",
      ta.updated_at as "updatedAt",
      u.email as "userEmail",
      u.first_name as "userFirstName",
      u.last_name as "userLastName",
      u.image_url as "userImageUrl",
      ab.email as "assignedByEmail",
      ab.first_name as "assignedByFirstName",
      ab.last_name as "assignedByLastName"
    FROM task_assignments ta
    INNER JOIN users u ON ta.user_id = u.id
    LEFT JOIN users ab ON ta.assigned_by = ab.id
    WHERE ta.task_id = $1 AND ta.is_leader = true AND ta.is_active = true
    ORDER BY ta.assigned_at ASC
  `;

  try {
    const result: QueryResult = await db.query(query, [taskId]);
    return result.rows as TaskAssignmentWithUser[];
  } catch (error) {
    console.error('Error getting task leaders by task ID:', error);
    throw new Error(`Failed to get task leaders for task with ID: ${taskId}`);
  }
}
