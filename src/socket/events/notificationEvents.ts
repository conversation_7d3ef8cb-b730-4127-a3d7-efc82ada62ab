import { Socket } from "socket.io";
import {
  ClientToServerEvents,
  ServerToClientEvents,
  SocketData,
  SendNofication,
} from "../types";

export const handlePushNotification = (
  socket: Socket<ClientToServerEvents, ServerToClientEvents, any, SocketData>
) => {
  socket.on("send_notification", (data: SendNofication) => {
    try {
      console.log(
        `[${new Date().toISOString()}] 💬 Notification trigger to user ${
          socket.data.user.id
        }:`,
        JSON.stringify({ data: data })
      );
      switch (data.service) {
        case "task": {
          // Broadcast notification to all users in the kanban
          break;
        }
        case "department": {
          // Broadcast notification to all users in the department
          break;
        }
        default: {
          console.error(
            `[${new Date().toISOString()}] ❌ Unknown notification service: ${
              data.service
            }`
          );
          return;
        }
      }
    } catch (error) {
      console.error(
        `[${new Date().toISOString()}] ❌ Error sending notification:`,
        error
      );
    }
  });
};
